
package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.*;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class PdfToMarkdownConverterTest {

    private PdfToMarkdownConverter converter;
    @TempDir Path tempDir;
    @Mock PluginContext context;

    @BeforeEach
    void setUp() {
        PluginMetadata metadata = new PluginMetadata.Builder()
            .id("pdf-converter")
            .name("PDF to Markdown Converter")
            .version("1.0")
            .className("com.talkweb.ai.indexer.core.impl.PdfToMarkdownConverter")
            .build();
        converter = new PdfToMarkdownConverter(metadata);
    }

    @Test
    void shouldSupportPdfFiles() {
        assertTrue(converter.supportsExtension("pdf"));
        assertTrue(converter.supportsExtension("PDF"));
        assertFalse(converter.supportsExtension("docx"));
    }

    @Test
    void shouldConvertBasicText() throws Exception {
        File pdfFile = PdfTestUtils.createSimplePdf(tempDir, "test.pdf");
        var result = converter.convert(pdfFile);

        assertNotNull(result);
        // getMessage() returns the output path, so check if it ends with .md
        assertTrue(result.getMessage().endsWith(".md"),
            "Expected output path to end with .md, but got: " + result.getMessage());
        assertTrue(result.getContent().contains("Test PDF Document"));
    }

    @Test
    void shouldHandleInvalidPdf() {
        File invalidFile = tempDir.resolve("invalid.pdf").toFile();
        assertThrows(ConversionException.class, () -> converter.convert(invalidFile));
    }

    @Test
    void shouldConvertHeadings() throws Exception {
        File pdfFile = PdfTestUtils.createPdfWithHeadings(tempDir);
        var result = converter.convert(pdfFile);

        String content = result.getContent();
        // Check if content contains any text from the PDF
        assertTrue(content.contains("Main Heading") || content.contains("Subheading") || content.contains("Content"));
    }

    @Test
    void shouldPreserveParagraphs() throws Exception {
        File pdfFile = PdfTestUtils.createSimplePdf(tempDir, "paragraphs.pdf");
        var result = converter.convert(pdfFile);

        String content = result.getContent();
        assertTrue(content.contains("\n\n")); // 验证段落分隔
    }

    @Test
    void shouldHandleEmptyPdf() {
        assertThrows(ConversionException.class,
            () -> converter.convert(tempDir.resolve("empty.pdf").toFile()));
    }

    @Test
    void shouldConvertTables() throws Exception {
        File pdfFile = PdfTestUtils.createPdfWithTable(tempDir);
        var result = converter.convert(pdfFile);

        String content = result.getContent();
        // Check if content contains any text from the table
        assertTrue(content.contains("Header 1") && content.contains("Header 2"));
        assertTrue(content.contains("Cell 1") && content.contains("Cell 2"));
    }

    @Test
    void shouldConvertComplexHeadings() throws Exception {
        File pdfFile = PdfTestUtils.createPdfWithComplexHeadings(tempDir);
        var result = converter.convert(pdfFile);

        String content = result.getContent();
        // Check if content contains any text from the headings
        assertTrue(content.contains("Section 3.1"));
        assertTrue(content.contains("Subsection"));
    }

    @Test
    void shouldConvertNumberedLists() throws Exception {
        File pdfFile = PdfTestUtils.createPdfWithLists(tempDir);
        var result = converter.convert(pdfFile);

        String content = result.getContent();
        // Check if content contains any text from the lists
        assertTrue(content.contains("First item"));
        assertTrue(content.contains("Second item"));
    }

    // Enhanced functionality tests

    @Test
    @DisplayName("Should configure converter with custom settings")
    void shouldConfigureWithCustomSettings() {
        PdfToMarkdownConverter.PdfConversionConfig customConfig =
            PdfConversionConfigBuilder.newBuilder()
                .splitByPages(false)
                .preserveStructure(false)
                .extractImages(true)
                .maxPages(10)
                .build();

        converter.configure(customConfig);

        PdfToMarkdownConverter.PdfConversionConfig actualConfig = converter.getConfiguration();
        assertFalse(actualConfig.isSplitByPages());
        assertFalse(actualConfig.isPreserveStructure());
        assertTrue(actualConfig.isExtractImages());
        assertEquals(10, actualConfig.getMaxPages());
    }

    @Test
    @DisplayName("Should create high quality configuration")
    void shouldCreateHighQualityConfiguration() {
        PdfToMarkdownConverter.PdfConversionConfig config =
            PdfConversionConfigBuilder.createHighQualityConfig();

        assertTrue(config.isSplitByPages());
        assertTrue(config.isPreserveStructure());
        assertTrue(config.isIncludeMetadata());
        assertEquals(Integer.MAX_VALUE, config.getMaxPages());
    }

    @Test
    @DisplayName("Should handle plugin lifecycle correctly")
    void shouldHandlePluginLifecycleCorrectly() {
        assertEquals(PluginState.STOPPED, converter.getState());

        converter.init(context);
        assertEquals(PluginState.READY, converter.getState());

        converter.start();
        assertEquals(PluginState.RUNNING, converter.getState());

        converter.stop();
        assertEquals(PluginState.STOPPED, converter.getState());

        converter.destroy();
        assertEquals(PluginState.STOPPED, converter.getState());
    }

    @Test
    @DisplayName("Should handle PDF conversion exceptions correctly")
    void shouldHandlePdfConversionExceptionsCorrectly() {
        // Test file not found exception
        PdfConversionException fileNotFound = PdfConversionException.fileNotFound("test.pdf");
        assertEquals(PdfConversionException.ErrorType.FILE_NOT_FOUND, fileNotFound.getErrorType());
        assertEquals("test.pdf", fileNotFound.getFileName());
        assertTrue(fileNotFound.getDetailedMessage().contains("test.pdf"));

        // Test encrypted file exception
        PdfConversionException encrypted = PdfConversionException.encryptedFile("encrypted.pdf");
        assertEquals(PdfConversionException.ErrorType.ENCRYPTED_FILE, encrypted.getErrorType());
        assertFalse(encrypted.isRecoverable());

        // Test parsing error exception
        PdfConversionException parsing = PdfConversionException.parsingError("test.pdf", 5, new RuntimeException("test"));
        assertEquals(PdfConversionException.ErrorType.PARSING_ERROR, parsing.getErrorType());
        assertEquals(5, parsing.getPageNumber());
        assertTrue(parsing.isRecoverable());
    }

    @Test
    @DisplayName("Should validate configuration builder bounds")
    void shouldValidateConfigurationBuilderBounds() {
        PdfToMarkdownConverter.PdfConversionConfig config =
            PdfConversionConfigBuilder.newBuilder()
                .maxPages(-5) // Should be corrected to 1
                .build();

        assertEquals(1, config.getMaxPages());
    }

    @Test
    @DisplayName("Should create batch configuration correctly")
    void shouldCreateBatchConfigurationCorrectly() {
        PdfToMarkdownConverter.PdfConversionConfig config =
            PdfConversionConfigBuilder.createBatchConfig();

        assertTrue(config.isSplitByPages());
        assertTrue(config.isPreserveStructure());
        assertFalse(config.isExtractImages());
        assertTrue(config.isHandleEncrypted());
        assertTrue(config.isIncludeMetadata());
        assertEquals(100, config.getMaxPages());
    }
}
