
package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.*;
import com.talkweb.ai.indexer.util.HtmlConversionMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Collections;
import java.util.Map;

/**
 * Enhanced HTML to Markdown document processor plugin with improved structure preservation
 */
public class HtmlToMarkdownConverter implements DocumentProcessor {

    private static final Logger logger = LoggerFactory.getLogger(HtmlToMarkdownConverter.class);

    private final PluginMetadata metadata;
    private boolean initialized = true;
    private Map<String, Object> configuration = Collections.emptyMap();

    public HtmlToMarkdownConverter(PluginMetadata metadata) {
        this.metadata = metadata;
    }

    @Override
    public void destroy() {
        this.initialized = false;
    }
    
    @Override
    public void stop() throws PluginException {
        this.initialized = false;
    }

    @Override
    public PluginMetadata getMetadata() {
        return metadata;
    }

    @Override
    public PluginState getState() {
        return initialized ? PluginState.RUNNING : PluginState.STOPPED;
    }

    @Override
    public void init(PluginContext context) throws PluginException {
        this.initialized = true;
    }

    @Override
    public void start() throws PluginException {
        this.initialized = true;
    }

    @Override
    public String[] getSupportedExtensions() {
        return new String[]{"html", "htm"};
    }

    @Override
    public boolean supports(String extension) {
        if (extension == null) {
            return false;
        }
        String cleanExtension = extension.startsWith(".") ? extension.substring(1) : extension;
        for (String supportedExt : getSupportedExtensions()) {
            if (supportedExt.equalsIgnoreCase(cleanExtension)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Map<String, Object> getConfiguration() {
        return configuration;
    }

    @Override
    public void configure(Map<String, Object> config) {
        if (config != null) {
            this.configuration = config;
        }
    }

    @Override
    public ProcessingResult process(File inputFile, ProcessingContext context) throws DocumentProcessingException {
        if (inputFile == null || !inputFile.exists()) {
            logger.error("Input file is null or does not exist: {}", inputFile);
            return ProcessingResult.failure(new IllegalArgumentException("Input file is null or does not exist"));
        }

        if (!inputFile.isFile()) {
            logger.error("Input path is not a file: {}", inputFile.getPath());
            return ProcessingResult.failure(new IllegalArgumentException("Input path is not a file"));
        }

        HtmlConversionMode mode = context.getHtmlConversionMode();
        logger.debug("Processing HTML file: {} with mode: {}", inputFile.getPath(), mode);

        // Generate output file path
        String inputFileName = inputFile.getName();
        String outputFileName = getOutputFileName(inputFileName);
        Path outputPath = Path.of(inputFile.getParent(), outputFileName);

        try {
            // Read HTML content
            String htmlContent = java.nio.file.Files.readString(inputFile.toPath());
            logger.debug("Read HTML content from file: {} (size: {} characters)", inputFile.getPath(), htmlContent.length());

            // Convert to Markdown using optimized method for large files
            String markdown;
            if (htmlContent.length() > 100_000) {
                logger.debug("Using optimized conversion for large HTML file (size: {} characters)", htmlContent.length());
                markdown = com.talkweb.ai.indexer.util.HtmlToMarkdownConverter.convertLarge(htmlContent, mode);
            } else {
                markdown = com.talkweb.ai.indexer.util.HtmlToMarkdownConverter.convert(htmlContent, mode);
            }
            logger.debug("Converted HTML to Markdown (size: {} characters)", markdown.length());

            // Validate conversion result
            if (markdown == null || markdown.trim().isEmpty()) {
                logger.warn("Conversion resulted in empty content for file: {}", inputFile.getPath());
                if (mode == HtmlConversionMode.STRICT) {
                    throw new DocumentProcessingException("Conversion resulted in empty content in STRICT mode");
                }
                // In LOOSE mode, write a minimal markdown file
                markdown = "<!-- Empty content after HTML to Markdown conversion -->\n";
            }

            // Write Markdown content
            try {
                java.nio.file.Files.writeString(outputPath, markdown.trim());
                logger.info("Successfully converted HTML to Markdown: {} -> {}", inputFile.getPath(), outputPath);
                return ProcessingResult.success(outputPath.toFile());
            } catch (IOException e) {
                logger.error("Failed to write Markdown file: {}", outputPath, e);
                return ProcessingResult.failure(e);
            }

        } catch (IOException e) {
            logger.error("Failed to read HTML file: {}", inputFile.getPath(), e);
            return ProcessingResult.failure(e);
        } catch (Exception e) {
            logger.error("HTML to Markdown conversion failed for file: {}", inputFile.getPath(), e);
            if (mode == HtmlConversionMode.STRICT) {
                throw new DocumentProcessingException("HTML to Markdown conversion failed in STRICT mode for file: " + inputFile.getPath(), e);
            }
            return ProcessingResult.failure(e);
        }
    }

    /**
     * Generates output file name for the converted Markdown file
     */
    private String getOutputFileName(String inputFileName) {
        if (inputFileName == null || inputFileName.isEmpty()) {
            return "converted.md";
        }

        // Remove HTML extension and add .md
        String baseName = inputFileName;
        if (baseName.toLowerCase().endsWith(".html")) {
            baseName = baseName.substring(0, baseName.length() - 5);
        } else if (baseName.toLowerCase().endsWith(".htm")) {
            baseName = baseName.substring(0, baseName.length() - 4);
        }

        return baseName + ".md";
    }


}
